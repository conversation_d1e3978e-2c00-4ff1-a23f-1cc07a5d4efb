#!/usr/bin/env python3
"""
Interactive Database Test Script for Voice Agent Application

This script allows manual testing of database operations with user input.
You can test specific models and operations interactively.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

# Import database connection and models
from database.connection import DatabaseManager
from models.customer import Customer, CustomerNote, CustomerPreference
from models.appointment import Appointment
from models.recording import Recording
from models.service import Service, Package, PackageService, ServiceCategory
from models.staff import Staff, <PERSON><PERSON>, StaffSkill, StaffAvailability
from models.livekit import Room, TransferNumber

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class InteractiveDatabaseTester:
    """Interactive database testing utility"""
    
    def __init__(self):
        self.connected = False
    
    async def initialize(self):
        """Initialize database connection"""
        try:
            await DatabaseManager.initialize()
            # health_status = await DatabaseManager.health_check()
            # if health_status:
            #     self.connected = True
            #     print("✅ Database connection established successfully!")
            #     return True
            # else:
            #     print("❌ Database health check failed!")
            #     return False
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    
    async def test_customer_operations(self):
        """Test customer-related operations"""
        print("\n" + "="*50)
        print("CUSTOMER MODEL TESTING")
        print("="*50)
        
        while True:
            print("\nCustomer Operations:")
            print("1. List all customers")
            print("2. Search customer by phone")
            print("3. Search customer by email")
            print("4. Get customer count")
            print("5. Back to main menu")
            
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                await self._list_customers()
            elif choice == "2":
                phone = input("Enter phone number to search: ").strip()
                await self._search_customer_by_phone(phone)
            elif choice == "3":
                email = input("Enter email to search: ").strip()
                await self._search_customer_by_email(email)
            elif choice == "4":
                await self._get_customer_count()
            elif choice == "5":
                break
            else:
                print("Invalid choice. Please try again.")
    
    async def test_service_operations(self):
        """Test service-related operations"""
        print("\n" + "="*50)
        print("SERVICE MODEL TESTING")
        print("="*50)
        
        while True:
            print("\nService Operations:")
            print("1. List service categories")
            print("2. List services by category")
            print("3. List all packages")
            print("4. Get service details")
            print("5. Back to main menu")
            
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                await self._list_service_categories()
            elif choice == "2":
                category_id = input("Enter category ID: ").strip()
                await self._list_services_by_category(category_id)
            elif choice == "3":
                await self._list_packages()
            elif choice == "4":
                service_id = input("Enter service ID: ").strip()
                await self._get_service_details(service_id)
            elif choice == "5":
                break
            else:
                print("Invalid choice. Please try again.")
    
    async def test_appointment_operations(self):
        """Test appointment-related operations"""
        print("\n" + "="*50)
        print("APPOINTMENT MODEL TESTING")
        print("="*50)
        
        while True:
            print("\nAppointment Operations:")
            print("1. List recent appointments")
            print("2. Search appointments by status")
            print("3. Search appointments by customer phone")
            print("4. Get appointment statistics")
            print("5. Back to main menu")
            
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                limit = input("Enter number of appointments to show (default 10): ").strip()
                limit = int(limit) if limit.isdigit() else 10
                await self._list_recent_appointments(limit)
            elif choice == "2":
                status = input("Enter status (scheduled/completed/cancelled): ").strip()
                await self._search_appointments_by_status(status)
            elif choice == "3":
                phone = input("Enter customer phone number: ").strip()
                await self._search_appointments_by_customer_phone(phone)
            elif choice == "4":
                await self._get_appointment_statistics()
            elif choice == "5":
                break
            else:
                print("Invalid choice. Please try again.")
    
    async def test_staff_operations(self):
        """Test staff-related operations"""
        print("\n" + "="*50)
        print("STAFF MODEL TESTING")
        print("="*50)
        
        while True:
            print("\nStaff Operations:")
            print("1. List all staff")
            print("2. List active staff")
            print("3. List all skills")
            print("4. Get staff availability")
            print("5. Back to main menu")
            
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                await self._list_all_staff()
            elif choice == "2":
                await self._list_active_staff()
            elif choice == "3":
                await self._list_skills()
            elif choice == "4":
                staff_id = input("Enter staff ID (optional): ").strip()
                await self._get_staff_availability(staff_id if staff_id else None)
            elif choice == "5":
                break
            else:
                print("Invalid choice. Please try again.")
    
    # Helper methods for customer operations
    async def _list_customers(self):
        try:
            customers = await Customer.all().limit(10)
            print(f"\nFound {len(customers)} customers:")
            for customer in customers:
                print(f"- {customer.full_name} | Phone: {customer.phone_number} | Email: {customer.email}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _search_customer_by_phone(self, phone):
        try:
            customer = await Customer.filter(phone_number=phone).first()
            if customer:
                print(f"\nCustomer found:")
                print(f"Name: {customer.full_name}")
                print(f"Phone: {customer.phone_number}")
                print(f"Email: {customer.email}")
                print(f"Created: {customer.created_at}")
            else:
                print(f"No customer found with phone: {phone}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _search_customer_by_email(self, email):
        try:
            customer = await Customer.filter(email=email).first()
            if customer:
                print(f"\nCustomer found:")
                print(f"Name: {customer.full_name}")
                print(f"Phone: {customer.phone_number}")
                print(f"Email: {customer.email}")
            else:
                print(f"No customer found with email: {email}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _get_customer_count(self):
        try:
            count = await Customer.all().count()
            print(f"\nTotal customers in database: {count}")
        except Exception as e:
            print(f"Error: {e}")
    
    # Helper methods for service operations
    async def _list_service_categories(self):
        try:
            categories = await ServiceCategory.get_active_service_categories()
            print(f"\nFound {len(categories)} active categories:")
            for category in categories:
                print(f"- {category.name} (ID: {category.id})")
                if category.description:
                    print(f"  Description: {category.description}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _list_services_by_category(self, category_id):
        try:
            services = await Service.get_active_services(category_id)
            print(f"\nFound {len(services)} services in category:")
            for service in services:
                print(f"- {service.name} | ${service.amount} | {service.duration_minutes} min (ID: {service.id})")

                # Display skills required for this service
                if hasattr(service, 'service_skills') and service.service_skills:
                    skills_list = []
                    for service_skill in service.service_skills:
                        if hasattr(service_skill, 'skill') and service_skill.skill:
                            skills_list.append(service_skill.skill.name)

                    if skills_list:
                        print(f"  Skills Required: {', '.join(skills_list)}")
                    else:
                        print(f"  Skills Required: None specified")
                else:
                    print(f"  Skills Required: None specified")

                # Add description if available
                if service.description:
                    print(f"  Description: {service.description}")

                print()  # Add blank line for better readability
        except Exception as e:
            print(f"Error: {e}")
    
    async def _list_packages(self):
        try:
            packages = await Package.get_active_packages()
            print(f"\nFound {len(packages)} active packages:")
            for package in packages:
                print(f"- {package.name} | ${package.total_amount}")
                if package.description:
                    print(f"  Description: {package.description}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _get_service_details(self, service_id):
        try:
            service = await Service.get_service_detail(service_id)
            if service:
                print(f"\nService Details:")
                print(f"Name: {service.name}")
                print(f"Amount: ${service.amount}")
                print(f"Duration: {service.duration_minutes} minutes")
                print(f"Status: {service.status}")
                if service.description:
                    print(f"Description: {service.description}")
            else:
                print(f"No service found with ID: {service_id}")
        except Exception as e:
            print(f"Error: {e}")
    
    # Helper methods for appointment operations
    async def _list_recent_appointments(self, limit):
        try:
            appointments = await Appointment.all().limit(limit).prefetch_related('customer', 'staff')
            print(f"\nFound {len(appointments)} recent appointments:")
            for apt in appointments:
                customer_name = apt.customer.name if apt.customer else "Unknown"
                staff_name = apt.staff.name if apt.staff else "Unknown"
                print(f"- {customer_name} with {staff_name} | Status: {apt.status} | Date: {apt.appointment_date}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _search_appointments_by_status(self, status):
        try:
            appointments = await Appointment.filter(status=status).count()
            print(f"\nFound {appointments} appointments with status: {status}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _search_appointments_by_customer_phone(self, phone):
        try:
            customer = await Customer.filter(phone_number=phone).first()
            if customer:
                appointments = await Appointment.filter(customer=customer).count()
                print(f"\nFound {appointments} appointments for customer: {customer.full_name}")
            else:
                print(f"No customer found with phone: {phone}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _get_appointment_statistics(self):
        try:
            total = await Appointment.all().count()
            scheduled = await Appointment.filter(status="scheduled").count()
            completed = await Appointment.filter(status="completed").count()
            cancelled = await Appointment.filter(status="cancelled").count()
            
            print(f"\nAppointment Statistics:")
            print(f"Total: {total}")
            print(f"Scheduled: {scheduled}")
            print(f"Completed: {completed}")
            print(f"Cancelled: {cancelled}")
        except Exception as e:
            print(f"Error: {e}")
    
    # Helper methods for staff operations
    async def _list_all_staff(self):
        try:
            staff = await Staff.all()
            print(f"\nFound {len(staff)} staff members:")
            for member in staff:
                print(f"- {member.name} | Role: {member.role} | Status: {member.status}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _list_active_staff(self):
        try:
            staff = await Staff.filter(status="active")
            print(f"\nFound {len(staff)} active staff members:")
            for member in staff:
                print(f"- {member.name} | Role: {member.role}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _list_skills(self):
        try:
            skills = await Skill.all()
            print(f"\nFound {len(skills)} skills:")
            for skill in skills:
                print(f"- {skill.name}")
                if skill.description:
                    print(f"  Description: {skill.description}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def _get_staff_availability(self, staff_id):
        try:
            if staff_id:
                availability = await StaffAvailability.filter(staff_id=staff_id)
                print(f"\nAvailability for staff ID {staff_id}:")
            else:
                availability = await StaffAvailability.all().limit(10)
                print(f"\nShowing first 10 availability records:")
            
            for avail in availability:
                print(f"- Day: {avail.day_of_week} | {avail.start_time} - {avail.end_time}")
        except Exception as e:
            print(f"Error: {e}")
    
    async def run_interactive_tests(self):
        """Main interactive testing loop"""
        if not await self.initialize():
            return
        
        while True:
            print("\n" + "="*60)
            print("INTERACTIVE DATABASE TESTING MENU")
            print("="*60)
            print("1. Test Customer Operations")
            print("2. Test Service Operations")
            print("3. Test Appointment Operations")
            print("4. Test Staff Operations")
            print("5. Run Database Health Check")
            print("6. Exit")
            
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == "1":
                await self.test_customer_operations()
            elif choice == "2":
                await self.test_service_operations()
            elif choice == "3":
                await self.test_appointment_operations()
            elif choice == "4":
                await self.test_staff_operations()
            elif choice == "5":
                health = await DatabaseManager.health_check()
                print(f"Database health: {'✅ Healthy' if health else '❌ Unhealthy'}")
            elif choice == "6":
                print("Exiting...")
                break
            else:
                print("Invalid choice. Please try again.")
        
        # Close database connections
        await DatabaseManager.close()
        print("Database connections closed. Goodbye!")


async def main():
    """Main function"""
    tester = InteractiveDatabaseTester()
    await tester.run_interactive_tests()


if __name__ == "__main__":
    asyncio.run(main())