import time, asyncio, redis, json
from livekit.agents import Agent, RunContext, metrics, MetricsCollectedEvent, JobContext
from livekit.agents.llm import function_tool
from livekit import api
from livekit.plugins import openai, silero, deepgram, elevenlabs
from livekit.plugins.elevenlabs import VoiceSettings
from config.settings import agent_settings
from config.assistant import SYSTEM_PROMPT
from config.settings import settings
from models import Customer, Service, ServiceCategory, Appointment, Staff, Package, TransferNumber
from typing import Optional, List
from shared_types.context_variables import ContextVariables
from datetime import datetime, time, timezone, timedelta
from string import Template
from helpers import redis_helper
from livekit.plugins import google
from config import stt_creds
from dateutil import parser
from utils.prometheus_metric_collector import PrometheusExporter
from livekit.protocol.sip import CreateSIPParticipantRequest, TransferSIPParticipantRequest
from livekit.api import DeleteRoomRequest
import uuid

class Assistant(Agent):
    def __init__(self, instructions: str, context_vars: ContextVariables, prometheus_exporter=None) -> None:
        self.context_vars = context_vars
        self.is_appointment_created = False
        self.rclient = redis.Redis(
            host = settings.REDIS_HOST,
            port = settings.REDIS_PORT,
            db=settings.REDIS_DB,
            decode_responses=True,
            username=settings.REDIS_USERNAME,
            password=settings.REDIS_PASS
        )
        self.local_tz = timezone(timedelta(hours=5, minutes=30))  # IST timezone
        self.utc_tz = timezone.utc
        self.prometheus_exporter = prometheus_exporter or PrometheusExporter()
        self.usage_collector = metrics.UsageCollector()

        self.recent_eou_metrics: Optional[metrics.EOUMetrics] = None
        self.recent_llm_metrics: Optional[metrics.LLMMetrics] = None
        self.recent_tts_metrics: Optional[metrics.TTSMetrics] = None
        
        # voice_settings = VoiceSettings(speed=0.9, stability=0.8, similarity_boost=0.85)
        # voice_id= "FGY2WhTYpPnrIDTdsKH5",  voice_settings = VoiceSettings(speed=0.9, stability=0.7, similarity_boost=0.75)

        super().__init__(
            instructions=instructions,
            # stt = openai.STT(model="whisper-1", use_realtime= False),
            # stt=deepgram.STT(model="nova-3-general", interim_results=True),
            stt = google.STT(model="telephony"),
            # tts=google.TTS(
            #     gender="female",
            #     voice_name="en-US-Chirp3-HD-Autonoe",
            #     # voice_name="en-US-Chirp3-HD-Zephyr",
            #     # voice_name="en-US-Chirp3-HD-Leda",
            #     # voice_name="en-US-Chirp3-HD-Sulafat",
            #     use_streaming=True
            # ),
            # tts=deepgram.TTS(model="aura-2-andromeda-en"),
            # tts= openai.TTS(model='gpt-4o-mini-tts', voice = 'sage'),
            tts=elevenlabs.TTS(voice_id= "XcXEQzuLXRU9RcfWzEJt",  voice_settings = VoiceSettings(speed=0.9, stability=0.7, similarity_boost=0.75)),
            vad=silero.VAD.load(),
            llm=openai.LLM(model="gpt-4.1-mini-2025-04-14", temperature=0.7),
            # llm = google.LLM(model = "gemini-2.0-flash-001", temperature=0.8)
        )

        # super().__init__(
        #     instructions=instructions,
        #     llm=openai.realtime.RealtimeModel(
        #         voice="coral"
        #     )
        # )

        # super().__init__(
        #     instructions=instructions,
        #     llm=google.beta.realtime.RealtimeModel(
        #         model="gemini-live-2.5-flash",
        #         voice="Kore",
        #         temperature=0.8,
        #         instructions=instructions,
        #         vertexai=True
        #     )
        # )

    def _setup_event_handlers(self):
        """Setup event handlers for metrics collection"""
        
        @self.session.on("metrics_collected")
        def _on_metrics_collected(ev: MetricsCollectedEvent):
            # Log metrics using built-in logger
            metrics.log_metrics(ev.metrics)
            
            # Collect usage metrics
            self.usage_collector.collect(ev.metrics)
            
            # Export to Prometheus
            self.prometheus_exporter.export_metrics(ev.metrics)
            
            # Store metrics for conversation latency calculation
            self._store_metrics_for_latency(ev.metrics)
            
        @self.session.on("session_started")
        def _on_session_started():
            self.prometheus_exporter.increment_active_sessions()
            self.prometheus_exporter.set_session_info(
                session_id=self.jobCtx.room.name,
                room_name=self.jobCtx.room.name
            )
            
        @self.session.on("session_ended")
        def _on_session_ended():
            self.prometheus_exporter.decrement_active_sessions()

    @classmethod
    async def create(cls, jobCtx: JobContext, context_vars=None, prometheus_exporter=None):
        instructions = SYSTEM_PROMPT
        now = datetime.now()

        cls.jobCtx = jobCtx

        if context_vars:
            template = Template(SYSTEM_PROMPT)
            instructions = template.substitute(
                **context_vars,
                agent_name=agent_settings.AGENT_NAME,
                date=now.strftime("%d/%m/%Y"),
                day=now.strftime("%A"),
                time=now.strftime("%H:%M"),
            )

        return cls(instructions=instructions, context_vars=context_vars, prometheus_exporter=prometheus_exporter)

    @function_tool
    async def get_customer_detail(self):
        """Get customer details by phone number.

        Returns:
            str: Customer details.
        """
        try:

            if self.context_vars["customer"] is not None:
                return f'{self.context_vars["customer"].__dict__}'

            customer_dict = self.rclient.hgetall(
                f'customer{self.context_vars["phone_number"]}'
            )
            if customer_dict is not None:
                return f"{redis_helper.restore_from_redis(customer_dict)}"

        except:

            customer = await Customer.find_by_phone(
                phone_number=self.context_vars["phone_number"]
            )

            if customer:
                customer_dict = redis_helper.prepare_for_redis(customer.__dict__)
                key_name = f'customer{self.context_vars["phone_number"]}'
                self.rclient.hset(name=key_name, mapping=customer_dict)
                # self.rclient.expire(name=key_name, time=int(timedelta(minutes=30).total_seconds()))

                return f"{customer.__dict__}"

        return "New customer"

    @function_tool
    async def create_new_customer(self, first_name: str, last_name: str):
        """Create a new customer.

        Args:
            first_name (str): First name of the customer.
            last_name (str): Last name of the customer.

        Returns:
            str: Confirmation message or error message.
        """
        try:
            customer = await Customer.create_with_data(
                phone_number=self.context_vars["phone_number"],
                firstname=first_name,
                lastname=last_name,
            )
            self.context_vars["customer"] = customer
            return f"New customer created with ID: {customer.id}"
        except Exception as e:
            print(e)
            return f"Error creating customer: {str(e)}"

    @function_tool
    async def get_packages_offered(self):
        """Get available service packages with minimal details for browsing.

        Returns:
            str: Available packages with basic information including package_id, name,
                total amount, discount, and number of services. Featured packages are highlighted.
        """
        packages_data = None
        cache_key = "packages_minimal"

        # Try to get from Redis cache first
        try:
            cached_json = self.rclient.get(cache_key)
            if cached_json:
                packages_data = json.loads(cached_json)
        except Exception as e:
            print(f"Redis error: {e}")

        # If no cached data, fetch from database
        if not packages_data:
            try:
                db_packages = await Package.get_active_packages()

                if db_packages:
                    packages_data = []
                    for package in db_packages:
                        # Get service count without loading full service details
                        service_count = await package.package_services.all().count()

                        packages_data.append({
                            'id': str(package.id),
                            'name': package.name,
                            'type': package.type,
                            'description': getattr(package, 'description', None),
                            'total_amount': str(package.total_amount),
                            'total_duration_minutes': package.total_duration_minutes,
                            'discount': str(package.discount) if package.discount is not None else "0",
                            'discount_unit': package.discount_unit,
                            'is_featured': package.is_featured,
                            'service_count': service_count
                        })

                    # Cache the fresh data
                    self.rclient.set(
                        name=cache_key,
                        # time=int(timedelta(hours=1).total_seconds()),
                        value=json.dumps(packages_data)
                    )
            except Exception as e:
                print(f"Database error: {e}")
                return "Error retrieving packages"

        # Format the response
        if not packages_data:
            return "No packages available"

        # Separate featured and regular packages
        featured_packages = [pkg for pkg in packages_data if pkg.get('is_featured', False)]
        regular_packages = [pkg for pkg in packages_data if not pkg.get('is_featured', False)]

        return_arr = []

        # Add featured packages first
        if featured_packages:
            return_arr.append("🌟 FEATURED PACKAGES:")
            for i, package in enumerate(featured_packages):
                package_str = self._format_package_minimal_string(package, i + 1)
                return_arr.append(package_str)

        # Add regular packages
        if regular_packages:
            if featured_packages:
                return_arr.append("\n📦 OTHER PACKAGES:")
            start_index = len(featured_packages) + 1 if featured_packages else 1
            for i, package in enumerate(regular_packages):
                package_str = self._format_package_minimal_string(package, start_index + i)
                return_arr.append(package_str)

        return "\n".join(return_arr)

    @function_tool
    async def get_package_detail(self, package_id: str):
        """Get detailed information about a specific package including all included services and their required skills.

        Args:
            package_id (str): The unique identifier for the package

        Returns:
            str: Detailed package information including all included services with their details and skills required.
                 Also provides a summary of all unique skills needed for the entire package.
        """
        package_data = None
        cache_key = f"package_detail_{package_id}"

        # Try to get from Redis cache first
        try:
            cached_json = self.rclient.get(cache_key)
            if cached_json:
                package_data = json.loads(cached_json)
        except Exception as e:
            print(f"Redis error: {e}")

        # If no cached data, fetch from database
        if not package_data:
            try:
                package = await Package.get_package_detail(package_id=package_id)

                if not package:
                    return f"Package with ID {package_id} not found or not active"

                # Get related services for this package with their skills
                package_services = await package.package_services.all().prefetch_related("service__service_skills__skill")

                services_included = []
                service_ids = []
                all_skills = []  # Collect all unique skills from all services

                for ps in package_services:
                    service = ps.service
                    service_id = str(service.id)
                    service_ids.append(service_id)

                    # Get skills for this service
                    service_skills_data = []
                    if hasattr(service, "service_skills") and service.service_skills:
                        for service_skill in service.service_skills:
                            if hasattr(service_skill, "skill") and service_skill.skill:
                                skill_data = {
                                    "skill_id": str(service_skill.skill.id),
                                    "skill_name": service_skill.skill.name,
                                    "skill_description": getattr(service_skill.skill, "description", None),
                                }
                                service_skills_data.append(skill_data)

                                # Add to all_skills if not already present
                                if not any(skill["skill_id"] == skill_data["skill_id"] for skill in all_skills):
                                    all_skills.append(skill_data)

                    services_included.append({
                        'id': service_id,
                        'name': service.name,
                        'description': getattr(service, 'description', None),
                        'amount': str(service.amount),
                        'duration_minutes': service.duration_minutes,
                        'skills': service_skills_data
                    })

                package_data = {
                    'id': str(package.id),
                    'name': package.name,
                    'type': package.type,
                    'description': getattr(package, 'description', None),
                    'total_amount': str(package.total_amount),
                    'total_duration_minutes': package.total_duration_minutes,
                    'discount': str(package.discount) if package.discount is not None else "0",
                    'discount_unit': package.discount_unit,
                    'is_featured': package.is_featured,
                    'service_ids': service_ids,
                    'services_included': services_included,
                    'all_skills': all_skills  # All unique skills required for this package
                }

                # Cache the fresh data
                self.rclient.set(
                    name=cache_key,
                    # time=int(timedelta(hours=1).total_seconds()),
                    value=json.dumps(package_data)
                )
            except Exception as e:
                print(f"Database error: {e}")
                return f"Error retrieving package details: {str(e)}"

        # Format the detailed response
        return self._format_package_detail_string(package_data)

    def _format_package_minimal_string(self, package, index):
        """Helper method to format minimal package information string"""
        package_str = f"{index}. Package: {package['name']} (ID: {package['id']})"

        if package.get('type'):
            package_str += f" | Type: {package['type']}"

        package_str += f" | Total: {package['total_amount']} AED"

        if package.get('discount') and float(package['discount']) > 0:
            package_str += f" | 💰 Discount: {package['discount']}"
            if package.get('discount_unit'):
                package_str += f" {package['discount_unit']}"

        if package.get('total_duration_minutes'):
            hours = package['total_duration_minutes'] // 60
            minutes = package['total_duration_minutes'] % 60
            duration_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"
            package_str += f" | Duration: {duration_str}"

        # Add service count
        service_count = package.get('service_count', 0)
        package_str += f" | Includes {service_count} services"

        return package_str

    def _format_package_detail_string(self, package):
        """Helper method to format detailed package information string"""
        package_str = f"📦 PACKAGE DETAILS:\n"
        package_str += f"Package: {package['name']} (ID: {package['id']})\n"

        if package.get('type'):
            package_str += f"Type: {package['type']}\n"

        if package.get('description'):
            package_str += f"Description: {package['description']}\n"

        package_str += f"Total Amount: {package['total_amount']}\n"

        if package.get('discount') and float(package['discount']) > 0:
            package_str += f"💰 Discount: {package['discount']}"
            if package.get('discount_unit'):
                package_str += f" {package['discount_unit']}"
            package_str += "\n"

        if package.get('total_duration_minutes'):
            hours = package['total_duration_minutes'] // 60
            minutes = package['total_duration_minutes'] % 60
            duration_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"
            package_str += f"Total Duration: {duration_str}\n"

        # Add included services with their skills
        services = package.get('services_included', [])
        service_ids = package.get('service_ids', [])
        if services:
            package_str += f"\n🎯 INCLUDED SERVICES ({len(services)} services):\n"
            for i, service in enumerate(services):
                package_str += f"  {i+1}. {service['name']} (ID: {service['id']}) - AED{service['amount']} ({service['duration_minutes']} min)\n"
                if service.get('description'):
                    package_str += f"     Description: {service['description']}\n"

                # Add skills required for this service
                service_skills = service.get('skills', [])
                if service_skills:
                    skills_names = [skill['skill_name'] for skill in service_skills]
                    package_str += f"     🔧 Skills Required: {', '.join(skills_names)}\n"

            # Add service IDs for easy reference
            package_str += f"\n📋 Service IDs: [{', '.join(service_ids)}]"

        # Add summary of all skills required for the package
        all_skills = package.get('all_skills', [])
        if all_skills:
            package_str += f"\n\n🛠️ ALL SKILLS REQUIRED FOR THIS PACKAGE ({len(all_skills)} unique skills):\n"
            for i, skill in enumerate(all_skills):
                package_str += f"  {i+1}. {skill['skill_name']} (ID: {skill['skill_id']})"
                if skill.get('skill_description'):
                    package_str += f" - {skill['skill_description']}"
                package_str += "\n"

            # Add skill IDs for easy reference
            skill_ids = [skill['skill_id'] for skill in all_skills]
            package_str += f"\n🔧 Skill IDs: [{', '.join(skill_ids)}]"

        return package_str
    
    @function_tool
    async def get_service_categories(self):
        """Get available service categories
        Returns:
            str: Available service categories with service_category_id, name and description
        """
        service_categories = None
        
        # Try to get from Redis cache first
        try:
            cached_json = self.rclient.get("serviceCategories")
            if cached_json:
                service_categories = json.loads(cached_json)
        except Exception as e:
            print(f"Redis error: {e}")
        
        # If no cached data, fetch from database
        if not service_categories:
            try:
                db_categories = await ServiceCategory.get_active_service_categories()
                if db_categories:
                    # Convert objects to dictionaries for serialization
                    service_categories = [
                        {
                            "id": str(category.id),
                            "name": category.name,
                            "description": category.description,
                        }
                        for category in db_categories
                    ]
                    # Cache the fresh data
                    self.rclient.set(
                        name="serviceCategories",
                        # time=int(timedelta(hours=1).total_seconds()),
                        value=json.dumps(service_categories)
                    )
            except Exception as e:
                print(f"Database error: {e}")
                return "Error retrieving service categories"
        
        # Format the response
        if not service_categories:
            return "No service categories available"
        
        return_arr = []
        for i, service_category in enumerate(service_categories):
            category_id = service_category.get("id")
            category_name = service_category.get("name")
            category_description = service_category.get("description")
            category_str = (
                f"{i+1}. (service_category_id: {category_id}, Name: {category_name}"
            )
            if category_description:
                category_str += f", Description: {category_description}"
            category_str += ")"
            return_arr.append(category_str)
        
        return ", ".join(return_arr)

    @function_tool
    async def get_services_offered(self, service_category_id: str):
        """Get Available services with skills required for that service from the chosen service category.
        Ley the customer know about our featured services first.
        If any service has a discount, make sure to let the customer know about it.

        Args:
            service_category_id (str): The service_category_id of the service category the user wants to inquire about
            
        Returns:
            str: Available services with some details classified as featured services and normal services.
        """
        services_with_skills_required = None
        cache_key = f"services{service_category_id}"

        # Try to get from Redis cache first
        try:
            cached_json = self.rclient.get(cache_key)
            if cached_json:
                services_with_skills_required = json.loads(cached_json)
        except Exception as e:
            print(f"Redis error: {e}")

        # If no cached data, fetch from database
        if not services_with_skills_required:
            try:
                db_services = await Service.get_active_services(service_category_id)

                if db_services:
                    # Convert objects to dictionaries for serialization
                    services_with_skills_required = []
                    for service in db_services:
                        service_skills_data = []
                        if (
                            hasattr(service, "service_skills")
                            and service.service_skills
                        ):
                            for service_skill in service.service_skills:
                                if (
                                    hasattr(service_skill, "skill")
                                    and service_skill.skill
                                ):
                                    service_skills_data.append(
                                        {
                                            "skill_id": str(service_skill.skill.id),
                                            "skill_name": service_skill.skill.name,
                                            "skill_description": getattr(
                                                service_skill.skill, "description", None
                                            ),
                                        }
                                    )

                        services_with_skills_required.append({
                            'id': str(service.id),
                            'name': service.name,
                            'description': getattr(service, 'description', None),
                            'amount': str(service.amount),
                            'duration_minutes': service.duration_minutes,
                            'category': str(service.category) if service.category else None,
                            'service_skills': service_skills_data,
                            'featured_service': service.is_featured,
                            'discount': str(service.discount) if service.discount is not None else "0",
                            'discount_unit': service.discount_unit
                        })

                    # Cache the fresh data
                    self.rclient.set(
                        name=cache_key,
                        # time=int(timedelta(hours=1).total_seconds()),
                        value=json.dumps(services_with_skills_required)
                    )
            except Exception as e:
                print(f"Database error: {e}")
                return "Error retrieving services"

        # Format the response
        if not services_with_skills_required:
            return "No services available for this category"

        # Separate featured and non-featured services
        featured_services = []
        normal_services = []
        
        for i, service in enumerate(services_with_skills_required):
            service_id = service.get('id')
            service_name = service.get('name')
            service_amount = service.get('amount')
            service_duration = service.get('duration_minutes')
            service_category = service.get('category')
            service_skills = service.get('service_skills', [])
            is_featured = service.get('featured_service')
            service_discount = service.get('discount')
            discount_unit = service.get('discount_unit')

            service_str = f"{i+1}. (service_id: {service_id}, Service_name: {service_name}, Category: {service_category}, Is_featured_service: {is_featured}, Amount: {service_amount} AED, Discount: {service_discount} {discount_unit}, Duration: {service_duration} Minutes Skills_Required=["

            skill_list = []
            for j, skill_data in enumerate(service_skills):
                # Check if skill_data is not None and is a dictionary
                if skill_data and isinstance(skill_data, dict):
                    skill_name = skill_data.get("skill_name")
                    skill_id = skill_data.get("skill_id")
                    skill_description = skill_data.get("skill_description")
                    skill_str = f"{j+1}. (skill_id={skill_id}, skill name={skill_name}"
                    if skill_description:
                        skill_str += f", description={skill_description}"
                    skill_str += ")"
                    skill_list.append(skill_str)
            
            service_str += ", ".join(skill_list)
            service_str += "])"
            
            # Add to appropriate list based on featured status
            if is_featured:
                featured_services.append(service_str)
            else:
                normal_services.append(service_str)

        # Combine lists with featured services first
        return_arr = []
        if featured_services:
            return_arr.append("The featured services are: " + ", ".join(featured_services))
        if normal_services:
            return_arr.append("Other Services are: " + ", ".join(normal_services))
        
        return " ".join(return_arr)

    @function_tool
    async def get_service_detail(self, service_id: str):
        """Get service details.
        Args:
            service_id (str): The ID of the service the user wants to inquire about.

        Returns:
                str: A description or details of the specified service.
        """
        service_detail = await Service.get_service_detail(service_id=service_id)

        return f"{service_detail}"

    @function_tool
    async def get_staffs(self, skill_ids: List["str"]):
        """Get staffs available with their skills and availability schedule.

        Args:
            skill_ids List["str"]: A list of skill ids from the service the customer have confirmed

        Returns:
                str: A list of available staffs, their skills, proficiency, and availability schedule
        """
        staff_data = None
        # Create cache key based on sorted skill_ids to ensure consistency
        sorted_skill_ids = sorted(skill_ids)
        cache_key = f"staffs_skills_{'-'.join(sorted_skill_ids)}"

        # Try to get from Redis cache first
        try:
            cached_json = self.rclient.get(cache_key)
            if cached_json:
                staff_data = json.loads(cached_json)
        except Exception as e:
            print(f"Redis error: {e}")

        # If no cached data, fetch from database
        if not staff_data:
            try:
                staff_data = await Staff.get_available_staff(skill_ids)

                self.rclient.set(
                    name=cache_key,
                    # time=int(timedelta(minutes=30).total_seconds()),
                    value=json.dumps(staff_data)
                )
            except Exception as e:
                print(f"Database error: {e}")
                return "Error retrieving staff information"

        # Format the response
        if not staff_data:
            return "No staff found with the required skills"

        rtrn_str = ""
        for i, staff in enumerate(staff_data):
            rtrn_str += f"({i+1}.staff_id={staff['id']}, name={staff['name']}, role={staff['role']}, notes={staff['notes']}, Skill_sets=["
            for j, skill in enumerate(staff['skills']):
                rtrn_str += f"{j+1}.({skill['proficiency_level']} in {skill['skill_name']}), "
            rtrn_str += "], "

            # Add availability information
            if staff['availability']:
                rtrn_str += "Availability=["
                for k, avail in enumerate(staff['availability']):
                    rtrn_str += f"{k+1}.({avail}), "
                rtrn_str += "]"
            else:
                rtrn_str += "Availability=[No schedule set]"

            rtrn_str += ") ,"

        return rtrn_str

    @function_tool
    async def get_staffs_by_appointment_id(self, appointment_id: str):
        """Get staff details by appointment id.

        Args:
            appointment_id str: The ID of the appointment you want to get the staff details for

        Returns:
                str: The details of the specific staff, their skills and proficiency
        """
        staff_data = None
        cache_key = f"staff_appointment_{appointment_id}"

        # Try to get from Redis cache first
        try:
            cached_json = self.rclient.get(cache_key)
            if cached_json:
                staff_data = json.loads(cached_json)
        except Exception as e:
            print(f"Redis error: {e}")

        # If no cached data, fetch from database
        if not staff_data:
            try:
                appointment_details = await Appointment.get_appointment_staff(
                    appointment_id=appointment_id
                )

                if not appointment_details or not appointment_details.staff:
                    return f"No staff found for appointment ID: {appointment_id}"

                staff = appointment_details.staff

                # Get staff skills
                staff_skills = await staff.staff_skills.all().prefetch_related("skill")
                skills_list = []
                for staff_skill in staff_skills:
                    skills_list.append({
                        'skill_id': str(staff_skill.skill.id),
                        'skill_name': staff_skill.skill.name,
                        'proficiency_level': staff_skill.proficiency_level
                    })

                staff_data = {
                    'id': str(staff.id),
                    'name': staff.name,
                    'role': staff.role,
                    'contact_ph': staff.contact_ph,
                    'contact_email': staff.contact_email,
                    'status': staff.status,
                    'branch': staff.branch,
                    'notes': staff.notes,
                    'skills': skills_list
                }

                # Cache the fresh data for 15 minutes (shorter cache for appointment-specific data)
                self.rclient.set(
                    name=cache_key,
                    # time=int(timedelta(minutes=15).total_seconds()),
                    value=json.dumps(staff_data)
                )
            except Exception as e:
                print(f"Database error: {e}")
                return f"Error retrieving staff details for appointment ID: {appointment_id}"

        # Format the response
        if not staff_data:
            return f"No staff found for appointment ID: {appointment_id}"

        skills_list = []
        for skill in staff_data['skills']:
            skill_info = f"skill_id={skill['skill_id']}, skill_name={skill['skill_name']}, proficiency={skill['proficiency_level']}"
            skills_list.append(f"({skill_info})")

        skills_str = ", ".join(skills_list) if skills_list else "No skills found"

        return f"staff_id={staff_data['id']}, name={staff_data['name']}, role={staff_data['role']}, notes={staff_data['notes']}, skills=[{skills_str}]"

    @function_tool
    async def get_staff_detail(self, staff_id: str):
        """Get details of a staff including skills and availability schedule.
        Args:
            staff_id (str): The ID of the staff you want the details for

        Returns:
                str: The details of the specific staff, their skills, proficiency, and availability schedule
        """
        staff_data = None
        cache_key = f"staff_detail_{staff_id}"

        # Try to get from Redis cache first
        try:
            cached_json = self.rclient.get(cache_key)
            if cached_json:
                staff_data = json.loads(cached_json)
        except Exception as e:
            print(f"Redis error: {e}")

        # If no cached data, fetch from database
        if not staff_data:
            try:
                staff_details = await Staff.get_staff_details(staff_id=staff_id)

                if not staff_details:
                    return f"No staff found with ID: {staff_id}"

                # Get the staff skills properly
                staff_skills = await staff_details.staff_skills.all().prefetch_related("skill")
                skills_list = []
                for staff_skill in staff_skills:
                    skills_list.append({
                        'skill_id': str(staff_skill.skill.id),
                        'skill_name': staff_skill.skill.name,
                        'proficiency_level': staff_skill.proficiency_level
                    })

                # Get availability schedule
                availability = await staff_details.get_availability()
                availability_list = []
                if availability:
                    for avail in availability:
                        availability_list.append(str(avail))

                staff_data = {
                    'id': str(staff_details.id),
                    'name': staff_details.name,
                    'role': staff_details.role,
                    'contact_ph': staff_details.contact_ph,
                    'contact_email': staff_details.contact_email,
                    'status': staff_details.status,
                    'branch': staff_details.branch,
                    'notes': staff_details.notes,
                    'skills': skills_list,
                    'availability': availability_list
                }

                # Cache the fresh data for 30 minutes
                self.rclient.set(
                    name=cache_key,
                    # time=int(timedelta(minutes=30).total_seconds()),
                    value=json.dumps(staff_data)
                )
            except Exception as e:
                print(f"Database error: {e}")
                return f"Error retrieving staff details for ID: {staff_id}"

        # Format the response
        if not staff_data:
            return f"No staff found with ID: {staff_id}"

        skills_list = []
        for skill in staff_data['skills']:
            skill_info = f"skill_id={skill['skill_id']}, skill_name={skill['skill_name']}, proficiency={skill['proficiency_level']}"
            skills_list.append(f"({skill_info})")

        skills_str = ", ".join(skills_list) if skills_list else "No skills found"

        availability_list = []
        for avail in staff_data['availability']:
            availability_list.append(f"({avail})")

        availability_str = ", ".join(availability_list) if availability_list else "No schedule set"

        return f"staff_id={staff_data['id']}, name={staff_data['name']}, role={staff_data['role']}, notes={staff_data['notes']}, skills=[{skills_str}], availability=[{availability_str}]"

    @function_tool
    async def get_prev_appointment(self):
        """
        Get previous appointments of the user.

        Returns:
            List of appointments of the user
        """
        cust_id = self.context_vars["customer"].id
        appointments = await Appointment.get_prev_appointments(customer_id=cust_id)
        return_str = ""

        for i, appt in enumerate(appointments):
            if hasattr(appt, 'appointment_date'):
                appt.appointment_date = appt.appointment_date.astimezone(self.local_tz)
                appt_details = f"Date: {appt.appointment_date.date()}, Time: {appt.appointment_date.time()}, Status: {appt.status}, Notes: {appt.notes} "
            
                return_str += f"{i+1}.[Appointment Details - ({appt_details}), (Service Details - {appt.service}), Staff Details - ({appt.staff})"

            else:
                return_str += f"{i+1}.[Appointment Details - ({appt}), (Service Details - {appt.service}), Staff Details - ({appt.staff})"
            if appt.recordings and appt.recordings[0].summary:
                summary = appt.recordings[0].summary
                return_str += f", Conversation_summary: {summary.strip().replace('**', '').replace('- ', '')}]"

            return_str += "\n"

        return return_str

    @function_tool
    async def create_or_reschedule_appointment(
        self,
        staff_id: str,
        service_id: Optional[str],
        package_id: Optional[str],
        appointment_date_time: datetime,
        amount: int,
        appointment_type: str,
        notes: Optional[str],
        runContext: RunContext,
    ):
        """
        Create a new appointment or reschedule an existing appointment for the customer with a specific staff member.

        Args:
            staff_id (str): The ID of the staff member who will handle the appointment.
            service_id (str): The ID of the service which the user wishes to take.
            package_id (str): The ID of the package which the user wishes to take.
            appointment_date_time (str): The appointment's date and time in ISO 8601 format (e.g., "2025-05-29T15:00:00Z").
            end_time (str): The appointment's end time in ISO 8601 format.
            amount (int): The amount of the service user wishes to take.
            notes (str): Any special requests from user

        Either service_id or package_id should be present, both should never be sent together
        Returns:
            str: A confirmation message or appointment ID upon successful booking.
        """
        try:
            # cust_id = self.context_vars["phone_number"]

            customer = await Customer.find_by_phone(
                phone_number=self.context_vars["phone_number"]
            )

            if not customer:
                return f"Customer does not exist. Please create a new customer"

            print("✅ staff_id -> ", staff_id)
            print("🫶🏻 Service id -> ", service_id)
            print("🫶🏻 Package id -> ", package_id)
            print("📝 appointment_date -> ", appointment_date_time)
            print("📝 appointment_type -> ", appointment_type)
            print("📝 amount -> ", amount)
            print("📝 notes -> ", notes)

            async def _speak_status_update(delay: float = 0.5):
                await asyncio.sleep(delay)
                await runContext.session.generate_reply(
                    instructions=f"""
                    You are scheduling an appointment, but it is taking a little while.
                    Update the user on your progress, but be very brief
                """
                )

            status_update_task = asyncio.create_task(_speak_status_update(2))

            if isinstance(appointment_date_time, str):
                # Parse ISO format string to datetime object
                appointment_date_time = parser.isoparse(appointment_date_time)

            appointment_date_time = appointment_date_time.astimezone(self.utc_tz)
            # Convert string IDs to UUID objects
            try:
                # Validate and convert UUIDs
                staff_uuid = uuid.UUID(staff_id) if staff_id else None
                service_uuid = uuid.UUID(service_id) if service_id else None
                package_uuid = uuid.UUID(package_id) if package_id else None
            except ValueError as e:
                return f"Invalid ID format: {str(e)}"

            res = await Appointment.create_appointment_with_segments(
                id=self.context_vars["appointment_id"],
                customer_id=customer.id,
                staff_id=staff_uuid,
                service_id=service_uuid,
                package_id=package_uuid,
                appointment_type=appointment_type,
                appointment_date=appointment_date_time,
                status="confirmed",
                amount=amount,
                notes=notes,
            )
            self.is_appointment_created = True

            status_update_task.cancel()

            return f"Successfully created appointment {res}"

        except Exception as e:
            print("Error on appointment -> ", e)
            return "Something went wrong while creating appointment. Please try again later"

    async def on_enter(self):
        self._setup_event_handlers()
        self.session.generate_reply()

    def _store_metrics_for_latency(self, metric_obj):
        """Store metrics for conversation latency calculation"""
        if isinstance(metric_obj, metrics.EOUMetrics):
            self.recent_eou_metrics = metric_obj
        elif isinstance(metric_obj, metrics.LLMMetrics):
            self.recent_llm_metrics = metric_obj
        elif isinstance(metric_obj, metrics.TTSMetrics):
            self.recent_tts_metrics = metric_obj
            
        # If we have all three metrics, calculate conversation latency
        if all([self.recent_eou_metrics, self.recent_llm_metrics, self.recent_tts_metrics]):
            # Check if they belong to the same speech_id (same conversation turn)
            if (hasattr(self.recent_eou_metrics, 'speech_id') and 
                hasattr(self.recent_llm_metrics, 'speech_id') and
                hasattr(self.recent_tts_metrics, 'speech_id')):
                
                if (self.recent_eou_metrics.speech_id == self.recent_llm_metrics.speech_id == 
                    self.recent_tts_metrics.speech_id):
                    
                    self.prometheus_exporter.calculate_conversation_latency(
                        self.recent_eou_metrics, 
                        self.recent_llm_metrics, 
                        self.recent_tts_metrics
                    )
                    
                    # Reset for next conversation turn
                    self.recent_eou_metrics = None
                    self.recent_llm_metrics = None
                    self.recent_tts_metrics = None
                    
    async def log_usage_summary(self):
        """Log usage summary"""
        summary = self.usage_collector.get_summary()
        print(f"Usage Summary: {summary}")
        return summary
